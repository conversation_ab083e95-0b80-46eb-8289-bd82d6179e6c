import { requireRole, getCurrentUser } from '@/lib/auth'
import { redirect } from 'next/navigation'

export default async function AdminRedirectPage() {
  console.log('[ADMIN REDIRECT] Accessing /dashboard/admin - redirecting to user-specific admin page')
  
  // Require admin or super-admin role
  const currentUser = await requireRole(['super-admin', 'admin'])
  
  console.log('[ADMIN REDIRECT] Current user:', currentUser.fullName, 'Role:', currentUser.role, 'ID:', currentUser.id)
  
  // Redirect to the user's own admin dashboard
  redirect(`/dashboard/admin/${currentUser.id}`)
}
